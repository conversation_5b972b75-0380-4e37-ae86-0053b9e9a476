# Supabase Configuration (Local Development)
NEXT_PUBLIC_SUPABASE_URL=https://djijalhrsxxvcyuefrus.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTE4MjcsImV4cCI6MjA2NjgyNzgyN30.b6TqzTcCfiNLI7tqLPYDui4WMJUCTF7lZ57WAmI4SaY
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTI1MTgyNywiZXhwIjoyMDY2ODI3ODI3fQ.AZBAIrXlidFelf1JyteZ-8kfeIEGPfP1W2qVEMg-hxw

# Google AI Configuration
# Get your API key from: https://makersuite.google.com/app/apikey
GOOGLE_AI_API_KEY=AIzaSyCtdcQHLSJuPwxX6vUvrTuEDoEAqqV6vCw

# OAuth Configuration (Optional for local development)
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret
GITHUB_CLIENT_ID=your_github_oauth_client_id
GITHUB_CLIENT_SECRET=your_github_oauth_client_secret

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# Database Configuration (Local Supabase)
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# Agents Service Configuration
NEXT_PUBLIC_AGENTS_URL=http://localhost:3001
