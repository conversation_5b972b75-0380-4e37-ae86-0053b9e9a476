'use client'

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { FluxCanvas } from '@/components/flux-canvas'
import {
  Zap,
  Play,
  Users,
  BarChart3,
  Target,
  ArrowRight,
  Sparkles,
  Rocket
} from 'lucide-react'

export default function Home() {
  const [session, setSession] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const supabase = createClientComponentClient()

  useEffect(() => {
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      setSession(session)
      setLoading(false)
    }

    getSession()
  }, [supabase])

  const handleStartCampaign = () => {
    // Bypass authentication and go directly to dashboard
    router.push('/dashboard')
  }

  const handleSignIn = () => {
    router.push('/auth')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="relative">
          <FluxCanvas intensity="medium" className="w-32 h-32" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-flux-500"></div>
          </div>
        </div>
      </div>
    )
  }

  // If user is already authenticated, redirect to dashboard
  if (session) {
    router.push('/dashboard')
    return null
  }

  return (
    <main className="min-h-screen bg-background relative overflow-hidden">
      {/* Background Flux Canvas */}
      <div className="absolute inset-0 opacity-30">
        <FluxCanvas intensity="low" />
      </div>

      {/* Hero Section */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4">
        <div className="text-center max-w-4xl mx-auto">
          {/* Logo/Brand */}
          <div className="flex items-center justify-center mb-8">
            <div className="w-16 h-16 rounded-full bg-flux-500/20 flex items-center justify-center mr-4">
              <Zap className="h-8 w-8 text-flux-500" />
            </div>
            <div className="text-left">
              <h1 className="text-4xl font-bold text-foreground">
                Metamorphic Flux
              </h1>
              <p className="text-lg text-muted-foreground">
                AI Marketing Engine
              </p>
            </div>
          </div>

          {/* Main Headline */}
          <h2 className="text-6xl md:text-7xl font-bold text-foreground mb-6 leading-tight">
            Shape-Shift Your
            <span className="flux-gradient bg-clip-text text-transparent block">
              Marketing Reality
            </span>
          </h2>

          {/* Subheadline */}
          <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
            Dynamic AI agents that morph campaign creative, channels, and spend in real-time.
            Generate personalized ads with <strong>Imagen 4</strong> and <strong>Veo 3</strong>,
            optimize with quantum computing, and achieve <strong>25%+ CTR uplift</strong>.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16">
            <Button
              size="lg"
              className="flux-gradient text-lg px-8 py-4 h-auto"
              onClick={handleStartCampaign}
            >
              <Rocket className="h-5 w-5 mr-2" />
              Start Campaign
              <ArrowRight className="h-5 w-5 ml-2" />
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="text-lg px-8 py-4 h-auto"
              onClick={handleSignIn}
            >
              <Users className="h-5 w-5 mr-2" />
              Sign In
            </Button>
          </div>

          {/* Feature Highlights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-card/50 backdrop-blur-sm border border-border rounded-lg p-6 transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 rounded-full bg-flux-500/20 flex items-center justify-center mb-4 mx-auto">
                <Sparkles className="h-6 w-6 text-flux-500" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">
                Real-Time DCO
              </h3>
              <p className="text-muted-foreground text-sm">
                &lt; 200ms idea-to-impression latency with AI-generated creative
              </p>
            </div>

            <div className="bg-card/50 backdrop-blur-sm border border-border rounded-lg p-6 transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mb-4 mx-auto">
                <Target className="h-6 w-6 text-green-500" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">
                Quantum Optimization
              </h3>
              <p className="text-muted-foreground text-sm">
                Google Willow-powered budget allocation with classical fallback
              </p>
            </div>

            <div className="bg-card/50 backdrop-blur-sm border border-border rounded-lg p-6 transition-all duration-300 hover:scale-105">
              <div className="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mb-4 mx-auto">
                <BarChart3 className="h-6 w-6 text-blue-500" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">
                Multi-Agent Swarm
              </h3>
              <p className="text-muted-foreground text-sm">
                6 specialized AI agents for copy, design, compliance & optimization
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="relative z-10 border-t border-border bg-card/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              © 2025 Metamorphic Flux™ - Digital Mind Pulse
            </p>
            <div className="flex items-center space-x-4">
              <span className="text-xs text-muted-foreground">
                Powered by React 19 • Next.js 15.3 • Supabase • Gemini 2.5
              </span>
            </div>
          </div>
        </div>
      </footer>
    </main>
  )
}
