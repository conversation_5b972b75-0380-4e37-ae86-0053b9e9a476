import { createClient } from '@supabase/supabase-js'
import { GoogleGenerativeAI } from '@google/generative-ai'

const supabaseUrl = process.env.SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY
const geminiApiKey = process.env.GOOGLE_AI_API_KEY

const supabase = createClient(supabaseUrl, supabaseKey)
const genAI = new GoogleGenerativeAI(geminiApiKey)

serve(async (req) => {
  const { table, id, text } = await req.json()

  if (!table || !id || !text) {
    return new Response(
      JSON.stringify({ error: 'Missing required fields: table, id, text' }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    )
  }

  try {
    const model = genAI.getGenerativeModel({ model: 'embedding-004' }) // or the latest appropriate model
    const result = await model.embedContent(text)
    const embedding = result.embedding.values

    const { data, error } = await supabase
      .from(table)
      .update({ embedding })
      .eq('id', id)
      .select()

    if (error) {
      throw error
    }

    return new Response(JSON.stringify({ data }), {
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
})
