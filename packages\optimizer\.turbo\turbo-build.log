[?9001h[?1004h[?25l[2J[m[H]0;C:\WINDOWS\system32\cmd.exe [?25h[?25l
> @metamorphic-flux/optimizer@1.0.0 build C:\Users\<USER>\Desktop\Metamorphic flux\packages\optimizer
> tsup[5;1H[?25h[34mCLI [mBuilding entry: {"index":"src/index.ts","quantum/index":"src/quantum/index.ts","classical/index":"src/classical/index.ts"}
[34mCLI [mUsing tsconfig: tsconfig.json
[34mCLI [mtsup v8.5.0
[34mCLI [mUsing tsup config: C:\Users\<USER>\Desktop\Metamorphic flux\packages\optimizer\tsup.config.ts
[34mCLI [mTarget: node18
[31mError: EBUSY: resource busy or locked, unlink 'C:\Users\<USER>\Desktop\Metamorphic flux\packages\optimizer\dis[m
[31m[8;112Hst\index.js.map'
[m
[31m    at Object.unlinkSync (node:fs:1954:11)
[m
[31m    at C:\Users\<USER>\Desktop\Metamorphic flux\node_modules\.pnpm\tsup@8.5.0_jiti@2.4.2_postcss@8.5.6_tsx@4.2[m
[31m[8;112H20.3_typescript@5.8.3_yaml@2.8.0\node_modules\tsup\dist\chunk-TWFEYLU4.js:143:73
[m
[31m    at Array.forEach (<anonymous>)
[m
[31m    at removeFiles (C:\Users\<USER>\Desktop\Metamorphic flux\node_modules\.pnpm\tsup@8.5.0_jiti@2.4.2_postcss@[m
[31m[8;112H@8.5.6_tsx@4.20.3_typescript@5.8.3_yaml@2.8.0\node_modules\tsup\dist\chunk-TWFEYLU4.js:143:9)
[m
[31m    at async buildAll (C:\Users\<USER>\Desktop\Metamorphic flux\node_modules\.pnpm\tsup@8.5.0_jiti@2.4.2_postc[m
[31m[8;112Hcss@8.5.6_tsx@4.20.3_typescript@5.8.3_yaml@2.8.0\node_modules\tsup\dist\index.js:1595:17)
[m
[31m    at async mainTasks (C:\Users\<USER>\Desktop\Metamorphic flux\node_modules\.pnpm\tsup@8.5.0_jiti@2.4.2_post[m
[31m[8;112Htcss@8.5.6_tsx@4.20.3_typescript@5.8.3_yaml@2.8.0\node_modules\tsup\dist\index.js:1699:13)
[m
[31m    at async Promise.all (index 1)
[m
[31m    at async C:\Users\<USER>\Desktop\Metamorphic flux\node_modules\.pnpm\tsup@8.5.0_jiti@2.4.2_postcss@8.5.6_t[m
[31m[8;112Htsx@4.20.3_typescript@5.8.3_yaml@2.8.0\node_modules\tsup\dist\index.js:1703:9
[m
[31m    at async Promise.all (index 0)
[m
[31m    at async build (C:\Users\<USER>\Desktop\Metamorphic flux\node_modules\.pnpm\tsup@8.5.0_jiti@2.4.2_postcss@[m
[31m[8;112H@8.5.6_tsx@4.20.3_typescript@5.8.3_yaml@2.8.0\node_modules\tsup\dist\index.js:1494:3)
[m
[30m[41m ELIFECYCLE [m [31mCommand failed with exit code 1.
[m
[?9001l[?1004l
