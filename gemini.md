# 🔮 METAMORPHIC FLUX — ZERO-SHOT GEMINI CLI PROMPT
# Date-stamp: 2025-07-01  |  Author: <PERSON> (Digital Mind Pulse)
# Purpose: Build the MVP for Metamorphic Flux™, a marketing engine that
# dynamically “shape-shifts” campaign creative, channels, and spend.

────────────────────────────────────────────────────────────────────────
## 1  GLOBAL OBJECTIVE
Design, build, test, and deploy a production-grade multi-agent system that:
• Generates & personalizes ads in real time (Imagen 4 stills, Veo 3 video).  
• Orchestrates micro-agents for copy, design, compliance, media buying.  
• Stores audience vectors + performance telemetry in Supabase pgvector.  
• Re-allocates budget via Quantum Optimizer (Google Willow fallback-safe).  
• Exposes a polished React 19 / Next.js 15.3 SaaS dashboard.

All external actions MUST run through Gemini CLI’s MCP tool routing and be
logged to Memory MCP.

────────────────────────────────────────────────────────────────────────
## 2  IMMUTABLE TECH STACK (mid-2025 “latest-of-each”)
Frontend ▸ React 19 | Next.js 15.3 | Vite 6 | Tailwind CSS 4 (Oxide)  
State    ▸ Zustand + React Query 5  
Backend  ▸ Supabase (Postgres 16) + pgvector 0.9  
AI       ▸ Gemini 2.5 Flash (copy), Gemini Ultra 2 (beta QA)  
Gen-Media▸ Imagen 4 (images) | Veo 3 (video)  
Agents   ▸ LangGraph v0.5.4 orchestration  
Workflows▸ Temporal.io Cloud 2025  
Quantum  ▸ Google Willow REST bridge (fallback to classical solver)  
Infra    ▸ Vercel Edge (web) | Supabase Edge Functions (server) | Cloudflare R2 (assets)  
CI/CD    ▸ GitHub + Jules App (auto PRs, tests, audio changelogs)

**STRICT ESLint rules:** gemini/no-hallucinated-types, no any, 100 % type-safety.

────────────────────────────────────────────────────────────────────────
## 3  MCP & TOOL ROUTING
1. Context 7 MCP – deep RAG on marketing & AI papers  
2. Browser MCP  – fetch latest docs/releases during build  
3. Supabase MCP  – database migrations & edge functions  
4. Brave Search MCP – competitive intel & prompt enrichment  
5. Memory MCP   – persistent task & log storage  
6. Image Gen    – calls Imagen 4 / Veo 3 via Gemini API

────────────────────────────────────────────────────────────────────────
## 4  DELIVERABLE CHECKLIST
- [ ] Monorepo (Turbo) with `/apps/web`, `/apps/agents`, `/packages/ui`.
- [ ] Supabase schema: users, personas, vectors, campaigns, creatives, logs.
- [ ] LangGraph YAML: ≥ 6 agents (Copy, Design, Analyst, Buyer, Ethics, QA).
- [ ] Temporal workflow for cross-channel creative swaps (< 50 ms SLA).
- [ ] Quantum Optimizer stub & classical fallback in `/packages/optimizer`.
- [ ] Flux Canvas: GSAP morph animation hero, dark-mode first.
- [ ] Unit + e2e tests (Playwright 1.48) aiming ≥ 95 % coverage.
- [ ] One-click Vercel deploy & Supabase migrations.
- [ ] README.md with env setup, run scripts, and API references.

────────────────────────────────────────────────────────────────────────
## 5  PHASE PLAN

### Phase 0 · Bootstrap (auto)
- init Git repo → setup Turbo, PNPM, ESLint preset, Husky hooks.
- scaffold Next.js 15.3 app with Tailwind 4 Oxide & shadcn/ui.
- generate Supabase SQL & seed scripts; run `supabase db push`.

### Phase 1 · Core Data & Auth
- implement Supabase OAuth & Row Level Security.
- add pgvector embedding service via Gemini Flash.

### Phase 2 · Agent Swarm
- build LangGraph agents; unit-test deterministic node transitions.
- connect Imagen 4 / Veo 3 wrappers with retry/backoff.

### Phase 3 · Canvas UI
- create Flux Canvas component: live preview of morphing creative.
- integrate Zustand store + optimistic UI updates.

### Phase 4 · Quantum Budgeting
- create `/optimizer` pkg, wrap Google Willow REST; log fallback results.

### Phase 5 · Workflows & Channels
- orchestrate email (Postmark), push (Firebase), ads (Meta/Google) via
  Temporal child-workflows; ensure idempotency on retry.

### Phase 6 · QA & Harden
- enforce strict ESLint; run Playwright & Jest; achieve 95 % cov.
- Lighthouse CI → PWA scores ≥ 95 across metrics.

### Phase 7 · Deploy & Pilot
- deploy web to Vercel Edge; set up preview channels.
- invite 15 pilot brands; capture metrics to `/logs`.

────────────────────────────────────────────────────────────────────────
## 6  SUCCESS CRITERIA
✓ Real-time DCO: < 200 ms idea-to-impression latency.  
✓ ≥ 25 % CTR uplift vs static control creatives.  
✓ MVP handles ≥ 10 M impressions/month on Supabase XL tier.  
✓ All database + quantum costs tracked & under $200/mo at launch.

────────────────────────────────────────────────────────────────────────
## 7  RISK MITIGATION
• **Model Drift** → nightly eval pipeline; auto-rollback on drop > 5 %.  
• **Compliance** → Ethics agent validates ads against IAB & local regs.  
• **Quantum Outage** → built-in linear programming fallback.  
• **API Quotas** → adaptive rate-limit guard in Browser MCP.

────────────────────────────────────────────────────────────────────────
## 8  NEXT ACTION
`BEGIN`   ← type this in Gemini CLI to kick off Phase 0 bootstrap.
