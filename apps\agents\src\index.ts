import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { logger } from '@/utils/logger';
import { AgentOrchestrator } from '@/workflow/orchestrator';
import { CopyAgent } from '@/agents/copy';
import { DesignAgent } from '@/agents/design';
import { AnalystAgent } from '@/agents/analyst';
import type { AgentContext } from '@/types';

// Load environment variables
dotenv.config();

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request logging middleware
app.use((req, res, next) => {
  logger.info('Incoming request', {
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
  });
  next();
});

// Initialize agents
const orchestrator = new AgentOrchestrator();
const copyAgent = new CopyAgent();
const designAgent = new DesignAgent();
const analystAgent = new AnalystAgent();

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'metamorphic-flux-agents',
    version: process.env.npm_package_version || '0.1.0',
    agents: {
      copy: 'ready',
      design: 'ready',
      analyst: 'ready',
      orchestrator: 'ready',
    },
  });
});

// Agent status endpoint
app.get('/agents/status', (req, res) => {
  res.json({
    agents: [
      {
        type: 'copy',
        name: 'Copy Agent',
        status: 'ready',
        model: 'gemini-1.5-flash',
        capabilities: ['headline', 'description', 'cta', 'full_copy'],
      },
      {
        type: 'design',
        name: 'Design Agent',
        status: 'ready',
        model: 'gemini-1.5-flash',
        capabilities: ['image', 'video', 'carousel'],
      },
      {
        type: 'analyst',
        name: 'Analyst Agent',
        status: 'ready',
        model: 'gemini-1.5-flash',
        capabilities: ['performance_analysis', 'trend_detection', 'optimization_recommendations'],
      },
    ],
    orchestrator: {
      status: 'ready',
      workflow_steps: ['copy_generation', 'design_generation', 'analysis', 'quality_check'],
    },
  });
});

// Execute full workflow
app.post('/workflow/execute', async (req, res) => {
  try {
    const context: AgentContext = req.body;

    // Validate required fields
    if (!context.campaignId || !context.userId || !context.organizationId) {
      res.status(400).json({
        error: 'Missing required fields: campaignId, userId, organizationId',
      });
      return;
    }

    logger.info('Starting workflow execution', {
      campaignId: context.campaignId,
      userId: context.userId,
      organizationId: context.organizationId,
    });

    const result = await orchestrator.executeWorkflow(context);

    res.json({
      success: true,
      executionId: result.executionId,
      status: result.status,
      results: result.results,
      errors: result.errors,
      duration: result.endTime ?
        result.endTime.getTime() - result.startTime.getTime() : null,
    });
    return;

  } catch (error) {
    logger.error('Workflow execution failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      body: req.body,
    });

    res.status(500).json({
      error: 'Workflow execution failed',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
    return;
  }
});

// Execute individual agents
app.post('/agents/:agentType/execute', async (req, res) => {
  try {
    const { agentType } = req.params;
    const { input, context } = req.body;

    if (!input || !context) {
      res.status(400).json({
        error: 'Missing required fields: input, context',
      });
      return;
    }

    let agent;
    switch (agentType) {
      case 'copy':
        agent = copyAgent;
        break;
      case 'design':
        agent = designAgent;
        break;
      case 'analyst':
        agent = analystAgent;
        break;
      default:
        res.status(400).json({
          error: `Unknown agent type: ${agentType}`,
        });
        return;
    }

    logger.info('Executing individual agent', {
      agentType,
      campaignId: context.campaignId,
      executionId: context.executionId,
    });

    const result = await agent.execute(input, context);

    res.json({
      success: true,
      agentType,
      result,
    });
    return;

  } catch (error) {
    logger.error('Agent execution failed', {
      agentType: req.params.agentType,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    res.status(500).json({
      error: 'Agent execution failed',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
    return;
  }
});

// Get agent capabilities
app.get('/agents/:agentType/capabilities', (req, res) => {
  const { agentType } = req.params;

  const capabilities: Record<string, any> = {
    copy: {
      inputSchema: copyAgent.inputSchema,
      outputSchema: copyAgent.outputSchema,
      formats: ['headline', 'description', 'cta', 'full_copy'],
      tones: ['professional', 'casual', 'friendly', 'innovative', 'urgent'],
    },
    design: {
      inputSchema: designAgent.inputSchema,
      outputSchema: designAgent.outputSchema,
      formats: ['image', 'video', 'carousel'],
      styles: ['modern', 'professional', 'creative', 'minimalist', 'vibrant'],
    },
    analyst: {
      inputSchema: analystAgent.inputSchema,
      outputSchema: analystAgent.outputSchema,
      metrics: ['ctr', 'cpc', 'cpm', 'conversion_rate', 'cpa', 'roas'],
      timeframes: ['24h', '7d', '30d', '90d'],
    },
  };

  if (!agentType || !capabilities[agentType]) {
    res.status(404).json({
      error: `Agent type not found: ${agentType}`,
    });
    return;
  }

  res.json(capabilities[agentType]);
  return;
});

// Error handling middleware
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
  });

  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.method} ${req.url} not found`,
  });
});

// Start server
app.listen(port, () => {
  logger.info('Metamorphic Flux Agents service started', {
    port,
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '0.1.0',
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
