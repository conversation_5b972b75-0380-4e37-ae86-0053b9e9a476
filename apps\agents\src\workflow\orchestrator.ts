import { StateGraph, END, START } from '@langchain/langgraph';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '@/utils/logger';
import { CopyAgent } from '@/agents/copy';
import { DesignAgent } from '@/agents/design';
import { AnalystAgent } from '@/agents/analyst';
import type {
  WorkflowState,
  AgentContext,
  AgentExecutionResult,
  AgentType
} from '@/types';

export class AgentOrchestrator {
  private copyAgent: CopyAgent;
  private designAgent: DesignAgent;
  private analystAgent: AnalystAgent;
  private workflow: StateGraph<WorkflowState>;

  constructor() {
    this.copyAgent = new CopyAgent();
    this.designAgent = new DesignAgent();
    this.analystAgent = new AnalystAgent();
    
    this.workflow = this.buildWorkflow();
  }

  private buildWorkflow(): StateGraph<WorkflowState> {
    const workflow = new StateGraph<WorkflowState>({
      channels: {
        campaignId: null,
        executionId: null,
        currentStep: null,
        context: null,
        results: null,
        errors: null,
        startTime: null,
        endTime: null,
        status: null,
      }
    });

    // Define workflow nodes
    workflow.addNode(START, this.startNode.bind(this));
    workflow.addNode('copy_generation', this.copyGenerationNode.bind(this));
    workflow.addNode('design_generation', this.designGenerationNode.bind(this));
    workflow.addNode('analysis', this.analysisNode.bind(this));
    workflow.addNode('quality_check', this.qualityCheckNode.bind(this));
    workflow.addNode('finalize', this.finalizeNode.bind(this));

    // Define workflow edges
    workflow.addEdge(START, 'copy_generation' as any);
    workflow.addEdge('copy_generation' as any, 'design_generation' as any);
    workflow.addEdge('design_generation' as any, 'analysis' as any);
    workflow.addEdge('analysis' as any, 'quality_check' as any);
    workflow.addEdge('quality_check' as any, 'finalize' as any);
    workflow.addEdge('finalize' as any, END);

    // Set entry point
    workflow.setEntryPoint(START as any);

    return workflow;
  }

  async executeWorkflow(context: AgentContext): Promise<WorkflowState> {
    const executionId = uuidv4();
    
    logger.info('Starting agent workflow execution', {
      executionId,
      campaignId: context.campaignId,
      userId: context.userId,
    });

    const initialState: WorkflowState = {
      campaignId: context.campaignId,
      executionId,
      currentStep: 'start',
      context: { ...context, executionId },
      results: {
        copy: null,
        design: null,
        analyst: null,
        buyer: null,
        ethics: null,
        qa: null,
      },
      errors: [],
      startTime: new Date(),
      status: 'running',
    };

    try {
      const compiledWorkflow = this.workflow.compile();
      const finalState = await compiledWorkflow.invoke(initialState);

      logger.info('Agent workflow execution completed', {
        executionId,
        campaignId: context.campaignId,
        status: finalState.status,
        duration: finalState.endTime ? 
          finalState.endTime.getTime() - finalState.startTime.getTime() : 0,
      });

      return finalState as WorkflowState;

    } catch (error) {
      logger.error('Agent workflow execution failed', {
        executionId,
        campaignId: context.campaignId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        ...initialState,
        status: 'failed',
        endTime: new Date(),
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      };
    }
  }

  private async startNode(state: WorkflowState): Promise<Partial<WorkflowState>> {
    logger.info('Starting workflow', {
      executionId: state.executionId,
      campaignId: state.campaignId,
    });

    return {
      currentStep: 'copy_generation',
      status: 'running',
    };
  }

  private async copyGenerationNode(state: WorkflowState): Promise<Partial<WorkflowState>> {
    logger.info('Executing copy generation', {
      executionId: state.executionId,
      campaignId: state.campaignId,
    });

    try {
      // Generate copy for each persona
      const copyResults: AgentExecutionResult[] = [];
      
      for (const persona of state.context.personas) {
        const copyInput = {
          persona,
          objective: state.context.objectives?.primary || 'brand_awareness',
          tone: this.determineTone(persona, state.context.campaign),
          format: 'full_copy',
          constraints: {
            maxLength: 280,
            keywords: state.context.objectives?.keywords || [],
            brandGuidelines: state.context.constraints?.brandGuidelines,
          },
        };

        const result = await this.copyAgent.execute(copyInput, state.context);
        copyResults.push(result);
      }

      // Select best copy result
      const bestCopyResult = this.selectBestResult(copyResults);

      return {
        currentStep: 'design_generation',
        results: {
          ...state.results,
          copy: bestCopyResult.output,
        },
      };

    } catch (error) {
      logger.error('Copy generation failed', {
        executionId: state.executionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        errors: [...state.errors, `Copy generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        status: 'failed',
      };
    }
  }

  private async designGenerationNode(state: WorkflowState): Promise<Partial<WorkflowState>> {
    logger.info('Executing design generation', {
      executionId: state.executionId,
      campaignId: state.campaignId,
    });

    try {
      if (!state.results.copy) {
        throw new Error('Copy generation result not available');
      }

      const designInput = {
        copy: {
          headline: state.results.copy.data.headline,
          description: state.results.copy.data.description,
          cta: state.results.copy.data.cta,
        },
        style: this.determineDesignStyle(state.context.personas, state.context.campaign),
        format: 'image' as const,
        dimensions: {
          width: 1200,
          height: 628,
        },
        constraints: {
          colorScheme: state.context.constraints?.colorScheme,
          brandColors: state.context.constraints?.brandColors,
          logoUrl: state.context.constraints?.logoUrl,
        },
      };

      const result = await this.designAgent.execute(designInput, state.context);

      return {
        currentStep: 'analysis',
        results: {
          ...state.results,
          design: result.output,
        },
      };

    } catch (error) {
      logger.error('Design generation failed', {
        executionId: state.executionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        errors: [...state.errors, `Design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        status: 'failed',
      };
    }
  }

  private async analysisNode(state: WorkflowState): Promise<Partial<WorkflowState>> {
    logger.info('Executing analysis', {
      executionId: state.executionId,
      campaignId: state.campaignId,
    });

    try {
      const analysisInput = {
        timeframe: '7d',
        metrics: ['ctr', 'cpc', 'conversion_rate', 'impressions', 'clicks'],
        campaignId: state.campaignId,
        creativeIds: state.context.creativeId ? [state.context.creativeId] : undefined,
      };

      const result = await this.analystAgent.execute(analysisInput, state.context);

      return {
        currentStep: 'quality_check',
        results: {
          ...state.results,
          analyst: result.output,
        },
      };

    } catch (error) {
      logger.error('Analysis failed', {
        executionId: state.executionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        errors: [...state.errors, `Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        status: 'failed',
      };
    }
  }

  private async qualityCheckNode(state: WorkflowState): Promise<Partial<WorkflowState>> {
    logger.info('Executing quality check', {
      executionId: state.executionId,
      campaignId: state.campaignId,
    });

    try {
      // Perform quality checks on all results
      const qualityScore = this.calculateQualityScore(state.results);
      const qualityThreshold = 0.7;

      if (qualityScore < qualityThreshold) {
        logger.warn('Quality check failed', {
          executionId: state.executionId,
          qualityScore,
          threshold: qualityThreshold,
        });

        return {
          errors: [...state.errors, `Quality check failed: score ${qualityScore} below threshold ${qualityThreshold}`],
          status: 'failed',
        };
      }

      return {
        currentStep: 'finalize',
      };

    } catch (error) {
      logger.error('Quality check failed', {
        executionId: state.executionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      return {
        errors: [...state.errors, `Quality check failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        status: 'failed',
      };
    }
  }

  private async finalizeNode(state: WorkflowState): Promise<Partial<WorkflowState>> {
    logger.info('Finalizing workflow', {
      executionId: state.executionId,
      campaignId: state.campaignId,
    });

    // Save results to database
    await this.saveWorkflowResults(state);

    return {
      currentStep: 'completed',
      status: 'completed',
      endTime: new Date(),
    };
  }

  private determineTone(persona: any, campaign: any): string {
    // Simple tone determination logic - could be enhanced with ML
    const age = persona.demographics?.age_range;
    const interests = persona.interests || [];

    if (interests.includes('technology') || interests.includes('innovation')) {
      return 'innovative';
    } else if (age && age.includes('25-35')) {
      return 'casual';
    } else if (campaign.objectives?.primary === 'brand_awareness') {
      return 'friendly';
    } else {
      return 'professional';
    }
  }

  private determineDesignStyle(personas: any[], campaign: any): string {
    // Simple style determination logic
    const hasYoungAudience = personas.some(p => 
      p.demographics?.age_range?.includes('25-35') || 
      p.demographics?.age_range?.includes('18-25')
    );

    const hasTechInterests = personas.some(p => 
      p.interests?.includes('technology') || 
      p.interests?.includes('innovation')
    );

    if (hasTechInterests) {
      return 'modern tech';
    } else if (hasYoungAudience) {
      return 'vibrant modern';
    } else {
      return 'professional clean';
    }
  }

  private selectBestResult(results: AgentExecutionResult[]): AgentExecutionResult {
    // Select result with highest confidence
    return results.reduce((best, current) => 
      current.output.confidence > best.output.confidence ? current : best
    );
  }

  private calculateQualityScore(results: Record<AgentType, any>): number {
    const scores: number[] = [];

    Object.values(results).forEach(result => {
      if (result && result.confidence) {
        scores.push(result.confidence);
      }
    });

    return scores.length > 0 ? 
      scores.reduce((sum, score) => sum + score, 0) / scores.length : 0;
  }

  private async saveWorkflowResults(state: WorkflowState): Promise<void> {
    // Implementation would save results to Supabase
    logger.info('Saving workflow results', {
      executionId: state.executionId,
      campaignId: state.campaignId,
    });
  }
}
