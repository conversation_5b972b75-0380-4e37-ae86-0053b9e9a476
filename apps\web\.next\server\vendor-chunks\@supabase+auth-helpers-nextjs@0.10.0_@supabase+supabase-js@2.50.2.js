"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+auth-helpers-nextjs@0.10.0_@supabase+supabase-js@2.50.2";
exports.ids = ["vendor-chunks/@supabase+auth-helpers-nextjs@0.10.0_@supabase+supabase-js@2.50.2"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-helpers-nextjs@0.10.0_@supabase+supabase-js@2.50.2/node_modules/@supabase/auth-helpers-nextjs/dist/index.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-helpers-nextjs@0.10.0_@supabase+supabase-js@2.50.2/node_modules/@supabase/auth-helpers-nextjs/dist/index.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  createBrowserSupabaseClient: () => createBrowserSupabaseClient,\n  createClientComponentClient: () => createClientComponentClient,\n  createMiddlewareClient: () => createMiddlewareClient,\n  createMiddlewareSupabaseClient: () => createMiddlewareSupabaseClient,\n  createPagesBrowserClient: () => createPagesBrowserClient,\n  createPagesServerClient: () => createPagesServerClient,\n  createRouteHandlerClient: () => createRouteHandlerClient,\n  createServerActionClient: () => createServerActionClient,\n  createServerComponentClient: () => createServerComponentClient,\n  createServerSupabaseClient: () => createServerSupabaseClient\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/clientComponentClient.ts\nvar import_auth_helpers_shared = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-helpers-shared@0.7.0_@supabase+supabase-js@2.50.2/node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar supabase;\nfunction createClientComponentClient({\n  supabaseUrl = \"https://djijalhrsxxvcyuefrus.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTE4MjcsImV4cCI6MjA2NjgyNzgyN30.b6TqzTcCfiNLI7tqLPYDui4WMJUCTF7lZ57WAmI4SaY\",\n  options,\n  cookieOptions,\n  isSingleton = true\n} = {}) {\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  const createNewClient = () => {\n    var _a;\n    return (0, import_auth_helpers_shared.createSupabaseClient)(supabaseUrl, supabaseKey, {\n      ...options,\n      global: {\n        ...options == null ? void 0 : options.global,\n        headers: {\n          ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n          \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n        }\n      },\n      auth: {\n        storage: new import_auth_helpers_shared.BrowserCookieAuthStorageAdapter(cookieOptions)\n      }\n    });\n  };\n  if (isSingleton) {\n    const _supabase = supabase ?? createNewClient();\n    if (typeof window === \"undefined\")\n      return _supabase;\n    if (!supabase)\n      supabase = _supabase;\n    return supabase;\n  }\n  return createNewClient();\n}\n\n// src/pagesBrowserClient.ts\nvar createPagesBrowserClient = createClientComponentClient;\n\n// src/pagesServerClient.ts\nvar import_auth_helpers_shared2 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-helpers-shared@0.7.0_@supabase+supabase-js@2.50.2/node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar import_set_cookie_parser = __webpack_require__(/*! set-cookie-parser */ \"(ssr)/../../node_modules/.pnpm/set-cookie-parser@2.7.1/node_modules/set-cookie-parser/lib/set-cookie.js\");\nvar NextServerAuthStorageAdapter = class extends import_auth_helpers_shared2.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n  }\n  getCookie(name) {\n    var _a, _b, _c;\n    const setCookie = (0, import_set_cookie_parser.splitCookiesString)(\n      ((_b = (_a = this.context.res) == null ? void 0 : _a.getHeader(\"set-cookie\")) == null ? void 0 : _b.toString()) ?? \"\"\n    ).map((c) => (0, import_auth_helpers_shared2.parseCookies)(c)[name]).find((c) => !!c);\n    const value = setCookie ?? ((_c = this.context.req) == null ? void 0 : _c.cookies[name]);\n    return value;\n  }\n  setCookie(name, value) {\n    this._setCookie(name, value);\n  }\n  deleteCookie(name) {\n    this._setCookie(name, \"\", {\n      maxAge: 0\n    });\n  }\n  _setCookie(name, value, options) {\n    var _a;\n    const setCookies = (0, import_set_cookie_parser.splitCookiesString)(\n      ((_a = this.context.res.getHeader(\"set-cookie\")) == null ? void 0 : _a.toString()) ?? \"\"\n    ).filter((c) => !(name in (0, import_auth_helpers_shared2.parseCookies)(c)));\n    const cookieStr = (0, import_auth_helpers_shared2.serializeCookie)(name, value, {\n      ...this.cookieOptions,\n      ...options,\n      // Allow supabase-js on the client to read the cookie as well\n      httpOnly: false\n    });\n    this.context.res.setHeader(\"set-cookie\", [...setCookies, cookieStr]);\n  }\n};\nfunction createPagesServerClient(context, {\n  supabaseUrl = \"https://djijalhrsxxvcyuefrus.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTE4MjcsImV4cCI6MjA2NjgyNzgyN30.b6TqzTcCfiNLI7tqLPYDui4WMJUCTF7lZ57WAmI4SaY\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared2.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextServerAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/middlewareClient.ts\nvar import_auth_helpers_shared3 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-helpers-shared@0.7.0_@supabase+supabase-js@2.50.2/node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar import_set_cookie_parser2 = __webpack_require__(/*! set-cookie-parser */ \"(ssr)/../../node_modules/.pnpm/set-cookie-parser@2.7.1/node_modules/set-cookie-parser/lib/set-cookie.js\");\nvar NextMiddlewareAuthStorageAdapter = class extends import_auth_helpers_shared3.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n  }\n  getCookie(name) {\n    var _a;\n    const setCookie = (0, import_set_cookie_parser2.splitCookiesString)(\n      ((_a = this.context.res.headers.get(\"set-cookie\")) == null ? void 0 : _a.toString()) ?? \"\"\n    ).map((c) => (0, import_auth_helpers_shared3.parseCookies)(c)[name]).find((c) => !!c);\n    if (setCookie) {\n      return setCookie;\n    }\n    const cookies = (0, import_auth_helpers_shared3.parseCookies)(this.context.req.headers.get(\"cookie\") ?? \"\");\n    return cookies[name];\n  }\n  setCookie(name, value) {\n    this._setCookie(name, value);\n  }\n  deleteCookie(name) {\n    this._setCookie(name, \"\", {\n      maxAge: 0\n    });\n  }\n  _setCookie(name, value, options) {\n    const newSessionStr = (0, import_auth_helpers_shared3.serializeCookie)(name, value, {\n      ...this.cookieOptions,\n      ...options,\n      // Allow supabase-js on the client to read the cookie as well\n      httpOnly: false\n    });\n    if (this.context.res.headers) {\n      this.context.res.headers.append(\"set-cookie\", newSessionStr);\n    }\n  }\n};\nfunction createMiddlewareClient(context, {\n  supabaseUrl = \"https://djijalhrsxxvcyuefrus.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTE4MjcsImV4cCI6MjA2NjgyNzgyN30.b6TqzTcCfiNLI7tqLPYDui4WMJUCTF7lZ57WAmI4SaY\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared3.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextMiddlewareAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/serverComponentClient.ts\nvar import_auth_helpers_shared4 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-helpers-shared@0.7.0_@supabase+supabase-js@2.50.2/node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar NextServerComponentAuthStorageAdapter = class extends import_auth_helpers_shared4.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n    this.isServer = true;\n  }\n  getCookie(name) {\n    var _a;\n    const nextCookies = this.context.cookies();\n    return (_a = nextCookies.get(name)) == null ? void 0 : _a.value;\n  }\n  setCookie(name, value) {\n  }\n  deleteCookie(name) {\n  }\n};\nfunction createServerComponentClient(context, {\n  supabaseUrl = \"https://djijalhrsxxvcyuefrus.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTE4MjcsImV4cCI6MjA2NjgyNzgyN30.b6TqzTcCfiNLI7tqLPYDui4WMJUCTF7lZ57WAmI4SaY\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared4.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextServerComponentAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/routeHandlerClient.ts\nvar import_auth_helpers_shared5 = __webpack_require__(/*! @supabase/auth-helpers-shared */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-helpers-shared@0.7.0_@supabase+supabase-js@2.50.2/node_modules/@supabase/auth-helpers-shared/dist/index.mjs\");\nvar NextRouteHandlerAuthStorageAdapter = class extends import_auth_helpers_shared5.CookieAuthStorageAdapter {\n  constructor(context, cookieOptions) {\n    super(cookieOptions);\n    this.context = context;\n  }\n  getCookie(name) {\n    var _a;\n    const nextCookies = this.context.cookies();\n    return (_a = nextCookies.get(name)) == null ? void 0 : _a.value;\n  }\n  setCookie(name, value) {\n    const nextCookies = this.context.cookies();\n    nextCookies.set(name, value, this.cookieOptions);\n  }\n  deleteCookie(name) {\n    const nextCookies = this.context.cookies();\n    nextCookies.set(name, \"\", {\n      ...this.cookieOptions,\n      maxAge: 0\n    });\n  }\n};\nfunction createRouteHandlerClient(context, {\n  supabaseUrl = \"https://djijalhrsxxvcyuefrus.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTE4MjcsImV4cCI6MjA2NjgyNzgyN30.b6TqzTcCfiNLI7tqLPYDui4WMJUCTF7lZ57WAmI4SaY\",\n  options,\n  cookieOptions\n} = {}) {\n  var _a;\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      \"either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!\"\n    );\n  }\n  return (0, import_auth_helpers_shared5.createSupabaseClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    global: {\n      ...options == null ? void 0 : options.global,\n      headers: {\n        ...(_a = options == null ? void 0 : options.global) == null ? void 0 : _a.headers,\n        \"X-Client-Info\": `${\"@supabase/auth-helpers-nextjs\"}@${\"0.10.0\"}`\n      }\n    },\n    auth: {\n      storage: new NextRouteHandlerAuthStorageAdapter(context, cookieOptions)\n    }\n  });\n}\n\n// src/serverActionClient.ts\nvar createServerActionClient = createRouteHandlerClient;\n\n// src/deprecated.ts\nfunction createBrowserSupabaseClient({\n  supabaseUrl = \"https://djijalhrsxxvcyuefrus.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTE4MjcsImV4cCI6MjA2NjgyNzgyN30.b6TqzTcCfiNLI7tqLPYDui4WMJUCTF7lZ57WAmI4SaY\",\n  options,\n  cookieOptions\n} = {}) {\n  console.warn(\n    \"Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages\"\n  );\n  return createPagesBrowserClient({\n    supabaseUrl,\n    supabaseKey,\n    options,\n    cookieOptions\n  });\n}\nfunction createServerSupabaseClient(context, {\n  supabaseUrl = \"https://djijalhrsxxvcyuefrus.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTE4MjcsImV4cCI6MjA2NjgyNzgyN30.b6TqzTcCfiNLI7tqLPYDui4WMJUCTF7lZ57WAmI4SaY\",\n  options,\n  cookieOptions\n} = {}) {\n  console.warn(\n    \"Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages\"\n  );\n  return createPagesServerClient(context, {\n    supabaseUrl,\n    supabaseKey,\n    options,\n    cookieOptions\n  });\n}\nfunction createMiddlewareSupabaseClient(context, {\n  supabaseUrl = \"https://djijalhrsxxvcyuefrus.supabase.co\",\n  supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTE4MjcsImV4cCI6MjA2NjgyNzgyN30.b6TqzTcCfiNLI7tqLPYDui4WMJUCTF7lZ57WAmI4SaY\",\n  options,\n  cookieOptions\n} = {}) {\n  console.warn(\n    \"Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware\"\n  );\n  return createMiddlewareClient(context, {\n    supabaseUrl,\n    supabaseKey,\n    options,\n    cookieOptions\n  });\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWhlbHBlcnMtbmV4dGpzQDAuMTAuMF9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi41MC4yL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvYXV0aC1oZWxwZXJzLW5leHRqcy9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLGtDQUFrQztBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLDRGQUE0RjtBQUN6SDtBQUNBO0FBQ0E7QUFDQSxvREFBb0Qsa0JBQWtCLGFBQWE7O0FBRW5GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0EsaUNBQWlDLG1CQUFPLENBQUMsZ01BQStCO0FBQ3hFO0FBQ0E7QUFDQSxnQkFBZ0IsMENBQW9DO0FBQ3BELGdCQUFnQixrTkFBeUM7QUFDekQ7QUFDQTtBQUNBO0FBQ0EsRUFBRSxJQUFJO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsZ0NBQWdDLEdBQUcsU0FBUztBQUMxRTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0Esa0NBQWtDLG1CQUFPLENBQUMsZ01BQStCO0FBQ3pFLCtCQUErQixtQkFBTyxDQUFDLGtJQUFtQjtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDBDQUFvQztBQUNwRCxnQkFBZ0Isa05BQXlDO0FBQ3pEO0FBQ0E7QUFDQSxFQUFFLElBQUk7QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsZ0NBQWdDLEdBQUcsU0FBUztBQUN4RTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQSxrQ0FBa0MsbUJBQU8sQ0FBQyxnTUFBK0I7QUFDekUsZ0NBQWdDLG1CQUFPLENBQUMsa0lBQW1CO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiwwQ0FBb0M7QUFDcEQsZ0JBQWdCLGtOQUF5QztBQUN6RDtBQUNBO0FBQ0EsRUFBRSxJQUFJO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGdDQUFnQyxHQUFHLFNBQVM7QUFDeEU7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0Esa0NBQWtDLG1CQUFPLENBQUMsZ01BQStCO0FBQ3pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsMENBQW9DO0FBQ3BELGdCQUFnQixrTkFBeUM7QUFDekQ7QUFDQTtBQUNBLEVBQUUsSUFBSTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixnQ0FBZ0MsR0FBRyxTQUFTO0FBQ3hFO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBLGtDQUFrQyxtQkFBTyxDQUFDLGdNQUErQjtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsMENBQW9DO0FBQ3BELGdCQUFnQixrTkFBeUM7QUFDekQ7QUFDQTtBQUNBLEVBQUUsSUFBSTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixnQ0FBZ0MsR0FBRyxTQUFTO0FBQ3hFO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxnQkFBZ0IsMENBQW9DO0FBQ3BELGdCQUFnQixrTkFBeUM7QUFDekQ7QUFDQTtBQUNBLEVBQUUsSUFBSTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLGdCQUFnQiwwQ0FBb0M7QUFDcEQsZ0JBQWdCLGtOQUF5QztBQUN6RDtBQUNBO0FBQ0EsRUFBRSxJQUFJO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsZ0JBQWdCLDBDQUFvQztBQUNwRCxnQkFBZ0Isa05BQXlDO0FBQ3pEO0FBQ0E7QUFDQSxFQUFFLElBQUk7QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxNQUFNLENBV0w7QUFDRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyZWRwYW5kYVxcRGVza3RvcFxcTWV0YW1vcnBoaWMgZmx1eFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK2F1dGgtaGVscGVycy1uZXh0anNAMC4xMC4wX0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjUwLjJcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxhdXRoLWhlbHBlcnMtbmV4dGpzXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2RlZlByb3AgPSBPYmplY3QuZGVmaW5lUHJvcGVydHk7XG52YXIgX19nZXRPd25Qcm9wRGVzYyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3I7XG52YXIgX19nZXRPd25Qcm9wTmFtZXMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcztcbnZhciBfX2hhc093blByb3AgPSBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5O1xudmFyIF9fZXhwb3J0ID0gKHRhcmdldCwgYWxsKSA9PiB7XG4gIGZvciAodmFyIG5hbWUgaW4gYWxsKVxuICAgIF9fZGVmUHJvcCh0YXJnZXQsIG5hbWUsIHsgZ2V0OiBhbGxbbmFtZV0sIGVudW1lcmFibGU6IHRydWUgfSk7XG59O1xudmFyIF9fY29weVByb3BzID0gKHRvLCBmcm9tLCBleGNlcHQsIGRlc2MpID0+IHtcbiAgaWYgKGZyb20gJiYgdHlwZW9mIGZyb20gPT09IFwib2JqZWN0XCIgfHwgdHlwZW9mIGZyb20gPT09IFwiZnVuY3Rpb25cIikge1xuICAgIGZvciAobGV0IGtleSBvZiBfX2dldE93blByb3BOYW1lcyhmcm9tKSlcbiAgICAgIGlmICghX19oYXNPd25Qcm9wLmNhbGwodG8sIGtleSkgJiYga2V5ICE9PSBleGNlcHQpXG4gICAgICAgIF9fZGVmUHJvcCh0bywga2V5LCB7IGdldDogKCkgPT4gZnJvbVtrZXldLCBlbnVtZXJhYmxlOiAhKGRlc2MgPSBfX2dldE93blByb3BEZXNjKGZyb20sIGtleSkpIHx8IGRlc2MuZW51bWVyYWJsZSB9KTtcbiAgfVxuICByZXR1cm4gdG87XG59O1xudmFyIF9fdG9Db21tb25KUyA9IChtb2QpID0+IF9fY29weVByb3BzKF9fZGVmUHJvcCh7fSwgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSksIG1vZCk7XG5cbi8vIHNyYy9pbmRleC50c1xudmFyIHNyY19leHBvcnRzID0ge307XG5fX2V4cG9ydChzcmNfZXhwb3J0cywge1xuICBjcmVhdGVCcm93c2VyU3VwYWJhc2VDbGllbnQ6ICgpID0+IGNyZWF0ZUJyb3dzZXJTdXBhYmFzZUNsaWVudCxcbiAgY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50OiAoKSA9PiBjcmVhdGVDbGllbnRDb21wb25lbnRDbGllbnQsXG4gIGNyZWF0ZU1pZGRsZXdhcmVDbGllbnQ6ICgpID0+IGNyZWF0ZU1pZGRsZXdhcmVDbGllbnQsXG4gIGNyZWF0ZU1pZGRsZXdhcmVTdXBhYmFzZUNsaWVudDogKCkgPT4gY3JlYXRlTWlkZGxld2FyZVN1cGFiYXNlQ2xpZW50LFxuICBjcmVhdGVQYWdlc0Jyb3dzZXJDbGllbnQ6ICgpID0+IGNyZWF0ZVBhZ2VzQnJvd3NlckNsaWVudCxcbiAgY3JlYXRlUGFnZXNTZXJ2ZXJDbGllbnQ6ICgpID0+IGNyZWF0ZVBhZ2VzU2VydmVyQ2xpZW50LFxuICBjcmVhdGVSb3V0ZUhhbmRsZXJDbGllbnQ6ICgpID0+IGNyZWF0ZVJvdXRlSGFuZGxlckNsaWVudCxcbiAgY3JlYXRlU2VydmVyQWN0aW9uQ2xpZW50OiAoKSA9PiBjcmVhdGVTZXJ2ZXJBY3Rpb25DbGllbnQsXG4gIGNyZWF0ZVNlcnZlckNvbXBvbmVudENsaWVudDogKCkgPT4gY3JlYXRlU2VydmVyQ29tcG9uZW50Q2xpZW50LFxuICBjcmVhdGVTZXJ2ZXJTdXBhYmFzZUNsaWVudDogKCkgPT4gY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnRcbn0pO1xubW9kdWxlLmV4cG9ydHMgPSBfX3RvQ29tbW9uSlMoc3JjX2V4cG9ydHMpO1xuXG4vLyBzcmMvY2xpZW50Q29tcG9uZW50Q2xpZW50LnRzXG52YXIgaW1wb3J0X2F1dGhfaGVscGVyc19zaGFyZWQgPSByZXF1aXJlKFwiQHN1cGFiYXNlL2F1dGgtaGVscGVycy1zaGFyZWRcIik7XG52YXIgc3VwYWJhc2U7XG5mdW5jdGlvbiBjcmVhdGVDbGllbnRDb21wb25lbnRDbGllbnQoe1xuICBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCxcbiAgc3VwYWJhc2VLZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSxcbiAgb3B0aW9ucyxcbiAgY29va2llT3B0aW9ucyxcbiAgaXNTaW5nbGV0b24gPSB0cnVlXG59ID0ge30pIHtcbiAgaWYgKCFzdXBhYmFzZVVybCB8fCAhc3VwYWJhc2VLZXkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICBcImVpdGhlciBORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwgYW5kIE5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIGVudiB2YXJpYWJsZXMgb3Igc3VwYWJhc2VVcmwgYW5kIHN1cGFiYXNlS2V5IGFyZSByZXF1aXJlZCFcIlxuICAgICk7XG4gIH1cbiAgY29uc3QgY3JlYXRlTmV3Q2xpZW50ID0gKCkgPT4ge1xuICAgIHZhciBfYTtcbiAgICByZXR1cm4gKDAsIGltcG9ydF9hdXRoX2hlbHBlcnNfc2hhcmVkLmNyZWF0ZVN1cGFiYXNlQ2xpZW50KShzdXBhYmFzZVVybCwgc3VwYWJhc2VLZXksIHtcbiAgICAgIC4uLm9wdGlvbnMsXG4gICAgICBnbG9iYWw6IHtcbiAgICAgICAgLi4ub3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5nbG9iYWwsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAuLi4oX2EgPSBvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLmdsb2JhbCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLmhlYWRlcnMsXG4gICAgICAgICAgXCJYLUNsaWVudC1JbmZvXCI6IGAke1wiQHN1cGFiYXNlL2F1dGgtaGVscGVycy1uZXh0anNcIn1AJHtcIjAuMTAuMFwifWBcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIGF1dGg6IHtcbiAgICAgICAgc3RvcmFnZTogbmV3IGltcG9ydF9hdXRoX2hlbHBlcnNfc2hhcmVkLkJyb3dzZXJDb29raWVBdXRoU3RvcmFnZUFkYXB0ZXIoY29va2llT3B0aW9ucylcbiAgICAgIH1cbiAgICB9KTtcbiAgfTtcbiAgaWYgKGlzU2luZ2xldG9uKSB7XG4gICAgY29uc3QgX3N1cGFiYXNlID0gc3VwYWJhc2UgPz8gY3JlYXRlTmV3Q2xpZW50KCk7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCIpXG4gICAgICByZXR1cm4gX3N1cGFiYXNlO1xuICAgIGlmICghc3VwYWJhc2UpXG4gICAgICBzdXBhYmFzZSA9IF9zdXBhYmFzZTtcbiAgICByZXR1cm4gc3VwYWJhc2U7XG4gIH1cbiAgcmV0dXJuIGNyZWF0ZU5ld0NsaWVudCgpO1xufVxuXG4vLyBzcmMvcGFnZXNCcm93c2VyQ2xpZW50LnRzXG52YXIgY3JlYXRlUGFnZXNCcm93c2VyQ2xpZW50ID0gY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50O1xuXG4vLyBzcmMvcGFnZXNTZXJ2ZXJDbGllbnQudHNcbnZhciBpbXBvcnRfYXV0aF9oZWxwZXJzX3NoYXJlZDIgPSByZXF1aXJlKFwiQHN1cGFiYXNlL2F1dGgtaGVscGVycy1zaGFyZWRcIik7XG52YXIgaW1wb3J0X3NldF9jb29raWVfcGFyc2VyID0gcmVxdWlyZShcInNldC1jb29raWUtcGFyc2VyXCIpO1xudmFyIE5leHRTZXJ2ZXJBdXRoU3RvcmFnZUFkYXB0ZXIgPSBjbGFzcyBleHRlbmRzIGltcG9ydF9hdXRoX2hlbHBlcnNfc2hhcmVkMi5Db29raWVBdXRoU3RvcmFnZUFkYXB0ZXIge1xuICBjb25zdHJ1Y3Rvcihjb250ZXh0LCBjb29raWVPcHRpb25zKSB7XG4gICAgc3VwZXIoY29va2llT3B0aW9ucyk7XG4gICAgdGhpcy5jb250ZXh0ID0gY29udGV4dDtcbiAgfVxuICBnZXRDb29raWUobmFtZSkge1xuICAgIHZhciBfYSwgX2IsIF9jO1xuICAgIGNvbnN0IHNldENvb2tpZSA9ICgwLCBpbXBvcnRfc2V0X2Nvb2tpZV9wYXJzZXIuc3BsaXRDb29raWVzU3RyaW5nKShcbiAgICAgICgoX2IgPSAoX2EgPSB0aGlzLmNvbnRleHQucmVzKSA9PSBudWxsID8gdm9pZCAwIDogX2EuZ2V0SGVhZGVyKFwic2V0LWNvb2tpZVwiKSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9iLnRvU3RyaW5nKCkpID8/IFwiXCJcbiAgICApLm1hcCgoYykgPT4gKDAsIGltcG9ydF9hdXRoX2hlbHBlcnNfc2hhcmVkMi5wYXJzZUNvb2tpZXMpKGMpW25hbWVdKS5maW5kKChjKSA9PiAhIWMpO1xuICAgIGNvbnN0IHZhbHVlID0gc2V0Q29va2llID8/ICgoX2MgPSB0aGlzLmNvbnRleHQucmVxKSA9PSBudWxsID8gdm9pZCAwIDogX2MuY29va2llc1tuYW1lXSk7XG4gICAgcmV0dXJuIHZhbHVlO1xuICB9XG4gIHNldENvb2tpZShuYW1lLCB2YWx1ZSkge1xuICAgIHRoaXMuX3NldENvb2tpZShuYW1lLCB2YWx1ZSk7XG4gIH1cbiAgZGVsZXRlQ29va2llKG5hbWUpIHtcbiAgICB0aGlzLl9zZXRDb29raWUobmFtZSwgXCJcIiwge1xuICAgICAgbWF4QWdlOiAwXG4gICAgfSk7XG4gIH1cbiAgX3NldENvb2tpZShuYW1lLCB2YWx1ZSwgb3B0aW9ucykge1xuICAgIHZhciBfYTtcbiAgICBjb25zdCBzZXRDb29raWVzID0gKDAsIGltcG9ydF9zZXRfY29va2llX3BhcnNlci5zcGxpdENvb2tpZXNTdHJpbmcpKFxuICAgICAgKChfYSA9IHRoaXMuY29udGV4dC5yZXMuZ2V0SGVhZGVyKFwic2V0LWNvb2tpZVwiKSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLnRvU3RyaW5nKCkpID8/IFwiXCJcbiAgICApLmZpbHRlcigoYykgPT4gIShuYW1lIGluICgwLCBpbXBvcnRfYXV0aF9oZWxwZXJzX3NoYXJlZDIucGFyc2VDb29raWVzKShjKSkpO1xuICAgIGNvbnN0IGNvb2tpZVN0ciA9ICgwLCBpbXBvcnRfYXV0aF9oZWxwZXJzX3NoYXJlZDIuc2VyaWFsaXplQ29va2llKShuYW1lLCB2YWx1ZSwge1xuICAgICAgLi4udGhpcy5jb29raWVPcHRpb25zLFxuICAgICAgLi4ub3B0aW9ucyxcbiAgICAgIC8vIEFsbG93IHN1cGFiYXNlLWpzIG9uIHRoZSBjbGllbnQgdG8gcmVhZCB0aGUgY29va2llIGFzIHdlbGxcbiAgICAgIGh0dHBPbmx5OiBmYWxzZVxuICAgIH0pO1xuICAgIHRoaXMuY29udGV4dC5yZXMuc2V0SGVhZGVyKFwic2V0LWNvb2tpZVwiLCBbLi4uc2V0Q29va2llcywgY29va2llU3RyXSk7XG4gIH1cbn07XG5mdW5jdGlvbiBjcmVhdGVQYWdlc1NlcnZlckNsaWVudChjb250ZXh0LCB7XG4gIHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMLFxuICBzdXBhYmFzZUtleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZLFxuICBvcHRpb25zLFxuICBjb29raWVPcHRpb25zXG59ID0ge30pIHtcbiAgdmFyIF9hO1xuICBpZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUtleSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgIFwiZWl0aGVyIE5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCBhbmQgTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkgZW52IHZhcmlhYmxlcyBvciBzdXBhYmFzZVVybCBhbmQgc3VwYWJhc2VLZXkgYXJlIHJlcXVpcmVkIVwiXG4gICAgKTtcbiAgfVxuICByZXR1cm4gKDAsIGltcG9ydF9hdXRoX2hlbHBlcnNfc2hhcmVkMi5jcmVhdGVTdXBhYmFzZUNsaWVudCkoc3VwYWJhc2VVcmwsIHN1cGFiYXNlS2V5LCB7XG4gICAgLi4ub3B0aW9ucyxcbiAgICBnbG9iYWw6IHtcbiAgICAgIC4uLm9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuZ2xvYmFsLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAuLi4oX2EgPSBvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLmdsb2JhbCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLmhlYWRlcnMsXG4gICAgICAgIFwiWC1DbGllbnQtSW5mb1wiOiBgJHtcIkBzdXBhYmFzZS9hdXRoLWhlbHBlcnMtbmV4dGpzXCJ9QCR7XCIwLjEwLjBcIn1gXG4gICAgICB9XG4gICAgfSxcbiAgICBhdXRoOiB7XG4gICAgICBzdG9yYWdlOiBuZXcgTmV4dFNlcnZlckF1dGhTdG9yYWdlQWRhcHRlcihjb250ZXh0LCBjb29raWVPcHRpb25zKVxuICAgIH1cbiAgfSk7XG59XG5cbi8vIHNyYy9taWRkbGV3YXJlQ2xpZW50LnRzXG52YXIgaW1wb3J0X2F1dGhfaGVscGVyc19zaGFyZWQzID0gcmVxdWlyZShcIkBzdXBhYmFzZS9hdXRoLWhlbHBlcnMtc2hhcmVkXCIpO1xudmFyIGltcG9ydF9zZXRfY29va2llX3BhcnNlcjIgPSByZXF1aXJlKFwic2V0LWNvb2tpZS1wYXJzZXJcIik7XG52YXIgTmV4dE1pZGRsZXdhcmVBdXRoU3RvcmFnZUFkYXB0ZXIgPSBjbGFzcyBleHRlbmRzIGltcG9ydF9hdXRoX2hlbHBlcnNfc2hhcmVkMy5Db29raWVBdXRoU3RvcmFnZUFkYXB0ZXIge1xuICBjb25zdHJ1Y3Rvcihjb250ZXh0LCBjb29raWVPcHRpb25zKSB7XG4gICAgc3VwZXIoY29va2llT3B0aW9ucyk7XG4gICAgdGhpcy5jb250ZXh0ID0gY29udGV4dDtcbiAgfVxuICBnZXRDb29raWUobmFtZSkge1xuICAgIHZhciBfYTtcbiAgICBjb25zdCBzZXRDb29raWUgPSAoMCwgaW1wb3J0X3NldF9jb29raWVfcGFyc2VyMi5zcGxpdENvb2tpZXNTdHJpbmcpKFxuICAgICAgKChfYSA9IHRoaXMuY29udGV4dC5yZXMuaGVhZGVycy5nZXQoXCJzZXQtY29va2llXCIpKSA9PSBudWxsID8gdm9pZCAwIDogX2EudG9TdHJpbmcoKSkgPz8gXCJcIlxuICAgICkubWFwKChjKSA9PiAoMCwgaW1wb3J0X2F1dGhfaGVscGVyc19zaGFyZWQzLnBhcnNlQ29va2llcykoYylbbmFtZV0pLmZpbmQoKGMpID0+ICEhYyk7XG4gICAgaWYgKHNldENvb2tpZSkge1xuICAgICAgcmV0dXJuIHNldENvb2tpZTtcbiAgICB9XG4gICAgY29uc3QgY29va2llcyA9ICgwLCBpbXBvcnRfYXV0aF9oZWxwZXJzX3NoYXJlZDMucGFyc2VDb29raWVzKSh0aGlzLmNvbnRleHQucmVxLmhlYWRlcnMuZ2V0KFwiY29va2llXCIpID8/IFwiXCIpO1xuICAgIHJldHVybiBjb29raWVzW25hbWVdO1xuICB9XG4gIHNldENvb2tpZShuYW1lLCB2YWx1ZSkge1xuICAgIHRoaXMuX3NldENvb2tpZShuYW1lLCB2YWx1ZSk7XG4gIH1cbiAgZGVsZXRlQ29va2llKG5hbWUpIHtcbiAgICB0aGlzLl9zZXRDb29raWUobmFtZSwgXCJcIiwge1xuICAgICAgbWF4QWdlOiAwXG4gICAgfSk7XG4gIH1cbiAgX3NldENvb2tpZShuYW1lLCB2YWx1ZSwgb3B0aW9ucykge1xuICAgIGNvbnN0IG5ld1Nlc3Npb25TdHIgPSAoMCwgaW1wb3J0X2F1dGhfaGVscGVyc19zaGFyZWQzLnNlcmlhbGl6ZUNvb2tpZSkobmFtZSwgdmFsdWUsIHtcbiAgICAgIC4uLnRoaXMuY29va2llT3B0aW9ucyxcbiAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAvLyBBbGxvdyBzdXBhYmFzZS1qcyBvbiB0aGUgY2xpZW50IHRvIHJlYWQgdGhlIGNvb2tpZSBhcyB3ZWxsXG4gICAgICBodHRwT25seTogZmFsc2VcbiAgICB9KTtcbiAgICBpZiAodGhpcy5jb250ZXh0LnJlcy5oZWFkZXJzKSB7XG4gICAgICB0aGlzLmNvbnRleHQucmVzLmhlYWRlcnMuYXBwZW5kKFwic2V0LWNvb2tpZVwiLCBuZXdTZXNzaW9uU3RyKTtcbiAgICB9XG4gIH1cbn07XG5mdW5jdGlvbiBjcmVhdGVNaWRkbGV3YXJlQ2xpZW50KGNvbnRleHQsIHtcbiAgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwsXG4gIHN1cGFiYXNlS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVksXG4gIG9wdGlvbnMsXG4gIGNvb2tpZU9wdGlvbnNcbn0gPSB7fSkge1xuICB2YXIgX2E7XG4gIGlmICghc3VwYWJhc2VVcmwgfHwgIXN1cGFiYXNlS2V5KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgXCJlaXRoZXIgTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIGFuZCBORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSBlbnYgdmFyaWFibGVzIG9yIHN1cGFiYXNlVXJsIGFuZCBzdXBhYmFzZUtleSBhcmUgcmVxdWlyZWQhXCJcbiAgICApO1xuICB9XG4gIHJldHVybiAoMCwgaW1wb3J0X2F1dGhfaGVscGVyc19zaGFyZWQzLmNyZWF0ZVN1cGFiYXNlQ2xpZW50KShzdXBhYmFzZVVybCwgc3VwYWJhc2VLZXksIHtcbiAgICAuLi5vcHRpb25zLFxuICAgIGdsb2JhbDoge1xuICAgICAgLi4ub3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5nbG9iYWwsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgIC4uLihfYSA9IG9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuZ2xvYmFsKSA9PSBudWxsID8gdm9pZCAwIDogX2EuaGVhZGVycyxcbiAgICAgICAgXCJYLUNsaWVudC1JbmZvXCI6IGAke1wiQHN1cGFiYXNlL2F1dGgtaGVscGVycy1uZXh0anNcIn1AJHtcIjAuMTAuMFwifWBcbiAgICAgIH1cbiAgICB9LFxuICAgIGF1dGg6IHtcbiAgICAgIHN0b3JhZ2U6IG5ldyBOZXh0TWlkZGxld2FyZUF1dGhTdG9yYWdlQWRhcHRlcihjb250ZXh0LCBjb29raWVPcHRpb25zKVxuICAgIH1cbiAgfSk7XG59XG5cbi8vIHNyYy9zZXJ2ZXJDb21wb25lbnRDbGllbnQudHNcbnZhciBpbXBvcnRfYXV0aF9oZWxwZXJzX3NoYXJlZDQgPSByZXF1aXJlKFwiQHN1cGFiYXNlL2F1dGgtaGVscGVycy1zaGFyZWRcIik7XG52YXIgTmV4dFNlcnZlckNvbXBvbmVudEF1dGhTdG9yYWdlQWRhcHRlciA9IGNsYXNzIGV4dGVuZHMgaW1wb3J0X2F1dGhfaGVscGVyc19zaGFyZWQ0LkNvb2tpZUF1dGhTdG9yYWdlQWRhcHRlciB7XG4gIGNvbnN0cnVjdG9yKGNvbnRleHQsIGNvb2tpZU9wdGlvbnMpIHtcbiAgICBzdXBlcihjb29raWVPcHRpb25zKTtcbiAgICB0aGlzLmNvbnRleHQgPSBjb250ZXh0O1xuICAgIHRoaXMuaXNTZXJ2ZXIgPSB0cnVlO1xuICB9XG4gIGdldENvb2tpZShuYW1lKSB7XG4gICAgdmFyIF9hO1xuICAgIGNvbnN0IG5leHRDb29raWVzID0gdGhpcy5jb250ZXh0LmNvb2tpZXMoKTtcbiAgICByZXR1cm4gKF9hID0gbmV4dENvb2tpZXMuZ2V0KG5hbWUpKSA9PSBudWxsID8gdm9pZCAwIDogX2EudmFsdWU7XG4gIH1cbiAgc2V0Q29va2llKG5hbWUsIHZhbHVlKSB7XG4gIH1cbiAgZGVsZXRlQ29va2llKG5hbWUpIHtcbiAgfVxufTtcbmZ1bmN0aW9uIGNyZWF0ZVNlcnZlckNvbXBvbmVudENsaWVudChjb250ZXh0LCB7XG4gIHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMLFxuICBzdXBhYmFzZUtleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZLFxuICBvcHRpb25zLFxuICBjb29raWVPcHRpb25zXG59ID0ge30pIHtcbiAgdmFyIF9hO1xuICBpZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUtleSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgIFwiZWl0aGVyIE5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCBhbmQgTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkgZW52IHZhcmlhYmxlcyBvciBzdXBhYmFzZVVybCBhbmQgc3VwYWJhc2VLZXkgYXJlIHJlcXVpcmVkIVwiXG4gICAgKTtcbiAgfVxuICByZXR1cm4gKDAsIGltcG9ydF9hdXRoX2hlbHBlcnNfc2hhcmVkNC5jcmVhdGVTdXBhYmFzZUNsaWVudCkoc3VwYWJhc2VVcmwsIHN1cGFiYXNlS2V5LCB7XG4gICAgLi4ub3B0aW9ucyxcbiAgICBnbG9iYWw6IHtcbiAgICAgIC4uLm9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuZ2xvYmFsLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAuLi4oX2EgPSBvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLmdsb2JhbCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLmhlYWRlcnMsXG4gICAgICAgIFwiWC1DbGllbnQtSW5mb1wiOiBgJHtcIkBzdXBhYmFzZS9hdXRoLWhlbHBlcnMtbmV4dGpzXCJ9QCR7XCIwLjEwLjBcIn1gXG4gICAgICB9XG4gICAgfSxcbiAgICBhdXRoOiB7XG4gICAgICBzdG9yYWdlOiBuZXcgTmV4dFNlcnZlckNvbXBvbmVudEF1dGhTdG9yYWdlQWRhcHRlcihjb250ZXh0LCBjb29raWVPcHRpb25zKVxuICAgIH1cbiAgfSk7XG59XG5cbi8vIHNyYy9yb3V0ZUhhbmRsZXJDbGllbnQudHNcbnZhciBpbXBvcnRfYXV0aF9oZWxwZXJzX3NoYXJlZDUgPSByZXF1aXJlKFwiQHN1cGFiYXNlL2F1dGgtaGVscGVycy1zaGFyZWRcIik7XG52YXIgTmV4dFJvdXRlSGFuZGxlckF1dGhTdG9yYWdlQWRhcHRlciA9IGNsYXNzIGV4dGVuZHMgaW1wb3J0X2F1dGhfaGVscGVyc19zaGFyZWQ1LkNvb2tpZUF1dGhTdG9yYWdlQWRhcHRlciB7XG4gIGNvbnN0cnVjdG9yKGNvbnRleHQsIGNvb2tpZU9wdGlvbnMpIHtcbiAgICBzdXBlcihjb29raWVPcHRpb25zKTtcbiAgICB0aGlzLmNvbnRleHQgPSBjb250ZXh0O1xuICB9XG4gIGdldENvb2tpZShuYW1lKSB7XG4gICAgdmFyIF9hO1xuICAgIGNvbnN0IG5leHRDb29raWVzID0gdGhpcy5jb250ZXh0LmNvb2tpZXMoKTtcbiAgICByZXR1cm4gKF9hID0gbmV4dENvb2tpZXMuZ2V0KG5hbWUpKSA9PSBudWxsID8gdm9pZCAwIDogX2EudmFsdWU7XG4gIH1cbiAgc2V0Q29va2llKG5hbWUsIHZhbHVlKSB7XG4gICAgY29uc3QgbmV4dENvb2tpZXMgPSB0aGlzLmNvbnRleHQuY29va2llcygpO1xuICAgIG5leHRDb29raWVzLnNldChuYW1lLCB2YWx1ZSwgdGhpcy5jb29raWVPcHRpb25zKTtcbiAgfVxuICBkZWxldGVDb29raWUobmFtZSkge1xuICAgIGNvbnN0IG5leHRDb29raWVzID0gdGhpcy5jb250ZXh0LmNvb2tpZXMoKTtcbiAgICBuZXh0Q29va2llcy5zZXQobmFtZSwgXCJcIiwge1xuICAgICAgLi4udGhpcy5jb29raWVPcHRpb25zLFxuICAgICAgbWF4QWdlOiAwXG4gICAgfSk7XG4gIH1cbn07XG5mdW5jdGlvbiBjcmVhdGVSb3V0ZUhhbmRsZXJDbGllbnQoY29udGV4dCwge1xuICBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCxcbiAgc3VwYWJhc2VLZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSxcbiAgb3B0aW9ucyxcbiAgY29va2llT3B0aW9uc1xufSA9IHt9KSB7XG4gIHZhciBfYTtcbiAgaWYgKCFzdXBhYmFzZVVybCB8fCAhc3VwYWJhc2VLZXkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICBcImVpdGhlciBORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwgYW5kIE5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIGVudiB2YXJpYWJsZXMgb3Igc3VwYWJhc2VVcmwgYW5kIHN1cGFiYXNlS2V5IGFyZSByZXF1aXJlZCFcIlxuICAgICk7XG4gIH1cbiAgcmV0dXJuICgwLCBpbXBvcnRfYXV0aF9oZWxwZXJzX3NoYXJlZDUuY3JlYXRlU3VwYWJhc2VDbGllbnQpKHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSwge1xuICAgIC4uLm9wdGlvbnMsXG4gICAgZ2xvYmFsOiB7XG4gICAgICAuLi5vcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLmdsb2JhbCxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgLi4uKF9hID0gb3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5nbG9iYWwpID09IG51bGwgPyB2b2lkIDAgOiBfYS5oZWFkZXJzLFxuICAgICAgICBcIlgtQ2xpZW50LUluZm9cIjogYCR7XCJAc3VwYWJhc2UvYXV0aC1oZWxwZXJzLW5leHRqc1wifUAke1wiMC4xMC4wXCJ9YFxuICAgICAgfVxuICAgIH0sXG4gICAgYXV0aDoge1xuICAgICAgc3RvcmFnZTogbmV3IE5leHRSb3V0ZUhhbmRsZXJBdXRoU3RvcmFnZUFkYXB0ZXIoY29udGV4dCwgY29va2llT3B0aW9ucylcbiAgICB9XG4gIH0pO1xufVxuXG4vLyBzcmMvc2VydmVyQWN0aW9uQ2xpZW50LnRzXG52YXIgY3JlYXRlU2VydmVyQWN0aW9uQ2xpZW50ID0gY3JlYXRlUm91dGVIYW5kbGVyQ2xpZW50O1xuXG4vLyBzcmMvZGVwcmVjYXRlZC50c1xuZnVuY3Rpb24gY3JlYXRlQnJvd3NlclN1cGFiYXNlQ2xpZW50KHtcbiAgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwsXG4gIHN1cGFiYXNlS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVksXG4gIG9wdGlvbnMsXG4gIGNvb2tpZU9wdGlvbnNcbn0gPSB7fSkge1xuICBjb25zb2xlLndhcm4oXG4gICAgXCJQbGVhc2UgdXRpbGl6ZSB0aGUgYGNyZWF0ZVBhZ2VzQnJvd3NlckNsaWVudGAgZnVuY3Rpb24gaW5zdGVhZCBvZiB0aGUgZGVwcmVjYXRlZCBgY3JlYXRlQnJvd3NlclN1cGFiYXNlQ2xpZW50YCBmdW5jdGlvbi4gTGVhcm4gbW9yZTogaHR0cHM6Ly9zdXBhYmFzZS5jb20vZG9jcy9ndWlkZXMvYXV0aC9hdXRoLWhlbHBlcnMvbmV4dGpzLXBhZ2VzXCJcbiAgKTtcbiAgcmV0dXJuIGNyZWF0ZVBhZ2VzQnJvd3NlckNsaWVudCh7XG4gICAgc3VwYWJhc2VVcmwsXG4gICAgc3VwYWJhc2VLZXksXG4gICAgb3B0aW9ucyxcbiAgICBjb29raWVPcHRpb25zXG4gIH0pO1xufVxuZnVuY3Rpb24gY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoY29udGV4dCwge1xuICBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCxcbiAgc3VwYWJhc2VLZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSxcbiAgb3B0aW9ucyxcbiAgY29va2llT3B0aW9uc1xufSA9IHt9KSB7XG4gIGNvbnNvbGUud2FybihcbiAgICBcIlBsZWFzZSB1dGlsaXplIHRoZSBgY3JlYXRlUGFnZXNTZXJ2ZXJDbGllbnRgIGZ1bmN0aW9uIGluc3RlYWQgb2YgdGhlIGRlcHJlY2F0ZWQgYGNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50YCBmdW5jdGlvbi4gTGVhcm4gbW9yZTogaHR0cHM6Ly9zdXBhYmFzZS5jb20vZG9jcy9ndWlkZXMvYXV0aC9hdXRoLWhlbHBlcnMvbmV4dGpzLXBhZ2VzXCJcbiAgKTtcbiAgcmV0dXJuIGNyZWF0ZVBhZ2VzU2VydmVyQ2xpZW50KGNvbnRleHQsIHtcbiAgICBzdXBhYmFzZVVybCxcbiAgICBzdXBhYmFzZUtleSxcbiAgICBvcHRpb25zLFxuICAgIGNvb2tpZU9wdGlvbnNcbiAgfSk7XG59XG5mdW5jdGlvbiBjcmVhdGVNaWRkbGV3YXJlU3VwYWJhc2VDbGllbnQoY29udGV4dCwge1xuICBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCxcbiAgc3VwYWJhc2VLZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSxcbiAgb3B0aW9ucyxcbiAgY29va2llT3B0aW9uc1xufSA9IHt9KSB7XG4gIGNvbnNvbGUud2FybihcbiAgICBcIlBsZWFzZSB1dGlsaXplIHRoZSBgY3JlYXRlTWlkZGxld2FyZUNsaWVudGAgZnVuY3Rpb24gaW5zdGVhZCBvZiB0aGUgZGVwcmVjYXRlZCBgY3JlYXRlTWlkZGxld2FyZVN1cGFiYXNlQ2xpZW50YCBmdW5jdGlvbi4gTGVhcm4gbW9yZTogaHR0cHM6Ly9zdXBhYmFzZS5jb20vZG9jcy9ndWlkZXMvYXV0aC9hdXRoLWhlbHBlcnMvbmV4dGpzI21pZGRsZXdhcmVcIlxuICApO1xuICByZXR1cm4gY3JlYXRlTWlkZGxld2FyZUNsaWVudChjb250ZXh0LCB7XG4gICAgc3VwYWJhc2VVcmwsXG4gICAgc3VwYWJhc2VLZXksXG4gICAgb3B0aW9ucyxcbiAgICBjb29raWVPcHRpb25zXG4gIH0pO1xufVxuLy8gQW5ub3RhdGUgdGhlIENvbW1vbkpTIGV4cG9ydCBuYW1lcyBmb3IgRVNNIGltcG9ydCBpbiBub2RlOlxuMCAmJiAobW9kdWxlLmV4cG9ydHMgPSB7XG4gIGNyZWF0ZUJyb3dzZXJTdXBhYmFzZUNsaWVudCxcbiAgY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50LFxuICBjcmVhdGVNaWRkbGV3YXJlQ2xpZW50LFxuICBjcmVhdGVNaWRkbGV3YXJlU3VwYWJhc2VDbGllbnQsXG4gIGNyZWF0ZVBhZ2VzQnJvd3NlckNsaWVudCxcbiAgY3JlYXRlUGFnZXNTZXJ2ZXJDbGllbnQsXG4gIGNyZWF0ZVJvdXRlSGFuZGxlckNsaWVudCxcbiAgY3JlYXRlU2VydmVyQWN0aW9uQ2xpZW50LFxuICBjcmVhdGVTZXJ2ZXJDb21wb25lbnRDbGllbnQsXG4gIGNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50XG59KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-helpers-nextjs@0.10.0_@supabase+supabase-js@2.50.2/node_modules/@supabase/auth-helpers-nextjs/dist/index.js\n");

/***/ })

};
;