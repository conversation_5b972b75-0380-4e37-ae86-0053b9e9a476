'use client';

import { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { Draggable } from 'gsap/Draggable';
import { useCanvasStore, type CanvasNode } from '@/lib/stores/canvas-store';
import {
  Brain,
  Palette,
  BarChart3,
  Target,
  Shield,
  CheckCircle,
  Users,
  Zap,
  MoreVertical,
  Copy,
  Trash2,
  Settings,
  Play,
  Pause,
  AlertCircle,
  Clock,
  Sparkles,
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface CanvasNodeProps {
  node: CanvasNode;
  isSelected?: boolean;
  isConnecting?: boolean;
  onConnect?: (nodeId: string) => void;
  className?: string;
}

export function CanvasNodeComponent({ 
  node, 
  isSelected = false, 
  isConnecting = false,
  onConnect,
  className = '' 
}: CanvasNodeProps): React.JSX.Element {
  const nodeRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  
  const {
    updateNode,
    removeNode,
    duplicateNode,
    selectNode,
    moveNode,
    saveToHistory,
  } = useCanvasStore();

  // Initialize draggable behavior
  useEffect(() => {
    if (!nodeRef.current) return;

    const draggable = Draggable.create(nodeRef.current, {
      type: 'x,y',
      bounds: 'body',
      onDragStart: () => {
        setIsDragging(true);
        saveToHistory();
        if (!isSelected) {
          selectNode(node.id);
        }
      },
      onDrag: function() {
        moveNode(node.id, { x: this.x, y: this.y });
      },
      onDragEnd: () => {
        setIsDragging(false);
        // Snap to grid if enabled
        const settings = useCanvasStore.getState().settings;
        if (settings.snapToGrid) {
          const snappedX = Math.round(node.position.x / settings.gridSize) * settings.gridSize;
          const snappedY = Math.round(node.position.y / settings.gridSize) * settings.gridSize;
          
          gsap.to(nodeRef.current, {
            x: snappedX,
            y: snappedY,
            duration: 0.2,
            ease: 'power2.out',
          });
          
          moveNode(node.id, { x: snappedX, y: snappedY });
        }
      },
    });

    return () => {
      draggable[0]?.kill();
    };
  }, [node.id, isSelected, selectNode, moveNode, saveToHistory]);

  // Animate node status changes
  useEffect(() => {
    if (!nodeRef.current) return;

    const element = nodeRef.current;
    
    switch (node.status) {
      case 'processing':
        gsap.to(element, {
          scale: 1.05,
          duration: 0.3,
          ease: 'power2.out',
        });
        break;
      case 'complete':
        gsap.fromTo(element, 
          { scale: 1.1 },
          { 
            scale: 1,
            duration: 0.5,
            ease: 'elastic.out(1, 0.5)',
          }
        );
        break;
      case 'error':
        gsap.fromTo(element,
          { x: -5 },
          {
            x: 5,
            duration: 0.1,
            repeat: 3,
            yoyo: true,
            ease: 'power2.inOut',
            onComplete: () => { gsap.set(element, { x: 0 }); },
          }
        );
        break;
    }
  }, [node.status]);

  // Animate selection state
  useEffect(() => {
    if (!nodeRef.current) return;

    if (isSelected) {
      gsap.to(nodeRef.current, {
        boxShadow: '0 0 0 2px hsl(var(--flux-500))',
        duration: 0.2,
        ease: 'power2.out',
      });
    } else {
      gsap.to(nodeRef.current, {
        boxShadow: '0 0 0 0px transparent',
        duration: 0.2,
        ease: 'power2.out',
      });
    }
  }, [isSelected]);

  const getNodeIcon = () => {
    switch (node.type) {
      case 'persona': return Users;
      case 'creative': return Palette;
      case 'channel': return Target;
      case 'metric': return BarChart3;
      case 'agent': return Brain;
      case 'workflow': return Zap;
      default: return CheckCircle;
    }
  };

  const getStatusIcon = () => {
    switch (node.status) {
      case 'processing': return <Sparkles className="h-3 w-3 text-yellow-500 animate-pulse" />;
      case 'complete': return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'error': return <AlertCircle className="h-3 w-3 text-red-500" />;
      case 'warning': return <AlertCircle className="h-3 w-3 text-yellow-500" />;
      default: return <Clock className="h-3 w-3 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (node.status) {
      case 'processing': return 'border-yellow-500/50 bg-yellow-500/10';
      case 'complete': return 'border-green-500/50 bg-green-500/10';
      case 'error': return 'border-red-500/50 bg-red-500/10';
      case 'warning': return 'border-yellow-500/50 bg-yellow-500/10';
      default: return 'border-gray-500/50 bg-gray-500/10';
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (isConnecting && onConnect) {
      onConnect(node.id);
      return;
    }
    
    selectNode(node.id, e.ctrlKey || e.metaKey);
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowContextMenu(true);
  };

  const handleDuplicate = () => {
    duplicateNode(node.id);
    setShowContextMenu(false);
  };

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this node?')) {
      removeNode(node.id);
    }
    setShowContextMenu(false);
  };

  const handleToggleStatus = () => {
    const newStatus = node.status === 'idle' ? 'processing' : 'idle';
    updateNode(node.id, { status: newStatus });
    setShowContextMenu(false);
  };

  const Icon = getNodeIcon();

  return (
    <>
      <div
        ref={nodeRef}
        className={`
          absolute cursor-move select-none transition-all duration-200
          ${getStatusColor()}
          border-2 rounded-xl p-4 backdrop-blur-sm
          hover:scale-105 hover:shadow-lg
          ${isDragging ? 'z-50' : 'z-10'}
          ${isConnecting ? 'cursor-crosshair' : 'cursor-move'}
          ${className}
        `}
        style={{
          left: node.position.x,
          top: node.position.y,
          width: node.size.width,
          height: node.size.height,
        }}
        onClick={handleClick}
        onContextMenu={handleContextMenu}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Node Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-lg bg-flux-500/20 flex items-center justify-center">
              <Icon className="h-4 w-4 text-flux-500" />
            </div>
            {getStatusIcon()}
          </div>
          
          {isHovered && (
            <Button
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0 opacity-70 hover:opacity-100"
              onClick={(e) => {
                e.stopPropagation();
                setShowContextMenu(true);
              }}
            >
              <MoreVertical className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Node Content */}
        <div className="flex-1">
          <h3 className="text-sm font-semibold text-foreground truncate mb-1">
            {node.title}
          </h3>
          {node.subtitle && (
            <p className="text-xs text-muted-foreground truncate">
              {node.subtitle}
            </p>
          )}
        </div>

        {/* Connection Points */}
        <div className="absolute -right-2 top-1/2 transform -translate-y-1/2">
          <div className="w-4 h-4 rounded-full bg-flux-500 border-2 border-background opacity-0 hover:opacity-100 transition-opacity cursor-pointer" />
        </div>
        <div className="absolute -left-2 top-1/2 transform -translate-y-1/2">
          <div className="w-4 h-4 rounded-full bg-flux-500 border-2 border-background opacity-0 hover:opacity-100 transition-opacity cursor-pointer" />
        </div>

        {/* Processing Animation */}
        {node.status === 'processing' && (
          <div className="absolute inset-0 rounded-xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-flux-500/20 to-transparent animate-pulse" />
          </div>
        )}

        {/* Selection Indicator */}
        {isSelected && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-flux-500 rounded-full border-2 border-background" />
        )}
      </div>

      {/* Context Menu */}
      {showContextMenu && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={() => setShowContextMenu(false)}
          />
          <div className="absolute z-50 bg-card border rounded-lg shadow-lg p-1 min-w-[150px]"
               style={{
                 left: node.position.x + node.size.width + 10,
                 top: node.position.y,
               }}>
            <Button
              size="sm"
              variant="ghost"
              className="w-full justify-start"
              onClick={handleToggleStatus}
            >
              {node.status === 'idle' ? (
                <Play className="h-4 w-4 mr-2" />
              ) : (
                <Pause className="h-4 w-4 mr-2" />
              )}
              {node.status === 'idle' ? 'Start' : 'Stop'}
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              className="w-full justify-start"
              onClick={handleDuplicate}
            >
              <Copy className="h-4 w-4 mr-2" />
              Duplicate
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              className="w-full justify-start"
            >
              <Settings className="h-4 w-4 mr-2" />
              Configure
            </Button>
            
            <div className="border-t my-1" />
            
            <Button
              size="sm"
              variant="ghost"
              className="w-full justify-start text-destructive hover:text-destructive"
              onClick={handleDelete}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </>
      )}
    </>
  );
}
